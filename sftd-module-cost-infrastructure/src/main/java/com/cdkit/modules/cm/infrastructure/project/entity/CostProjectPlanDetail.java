package com.cdkit.modules.cm.infrastructure.project.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.UnsupportedEncodingException;

/**
 * @Description: 项目计划明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
@Schema(description="cost_project_plan_detail对象")
@Data
@TableName("cost_project_plan_detail")
public class CostProjectPlanDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
    /**关联计划ID*/
    @Schema(description = "关联计划ID")
    private String planId;
    /**区块*/
	@Excel(name = "区块", width = 15)
    @Schema(description = "区块")
    private String block;
    /**平台设施*/
	@Excel(name = "平台设施", width = 15)
    @Schema(description = "平台设施")
    private String platformFacility;
    /**产品型号*/
	@Excel(name = "产品型号", width = 15)
    @Schema(description = "产品型号")
    private String productModel;
    /**物料名称*/
    @Excel(name = "物料名称", width = 15)
    @Schema(description = "物料名称")
    private String materialName;
    /**密度*/
	@Excel(name = "密度", width = 15)
    @Schema(description = "密度")
    private java.math.BigDecimal density;
    /**用量(吨)*/
	@Excel(name = "用量(吨)", width = 15)
    @Schema(description = "用量(吨)")
    private java.math.BigDecimal usageAmount;
    /**预计年处理量(油，方)*/
	@Excel(name = "预计年处理量(油，方)", width = 15)
    @Schema(description = "预计年处理量(油，方)")
    private java.math.BigDecimal estimatedAnnualOil;
    /**预计年处理量(水，方)*/
	@Excel(name = "预计年处理量(水，方)", width = 15)
    @Schema(description = "预计年处理量(水，方)")
    private java.math.BigDecimal estimatedAnnualWater;
    /**收费费率(元/方)*/
	@Excel(name = "收费费率(元/方)", width = 15)
    @Schema(description = "收费费率(元/方)")
    private java.math.BigDecimal feeRate;
    /**年度预算应收(油，万元)*/
	@Excel(name = "年度预算应收(油，万元)", width = 15)
    @Schema(description = "年度预算应收(油，万元)")
    private java.math.BigDecimal revenueOil;
    /**年度预算应收(水，万元)*/
	@Excel(name = "年度预算应收(水，万元)", width = 15)
    @Schema(description = "年度预算应收(水，万元)")
    private java.math.BigDecimal revenueWater;
    /**年度预算需求吨(吨)*/
	@Excel(name = "年度预算需求吨(吨)", width = 15)
    @Schema(description = "年度预算需求吨(吨)")
    private java.math.BigDecimal demandTon;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
